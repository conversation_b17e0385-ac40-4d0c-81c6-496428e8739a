import React, { useState, useEffect } from 'react';
import ScrollReveal from './ScrollReveal';
import { motion, AnimatePresence } from 'framer-motion';
import { Camera, Aperture, ChevronDown, ChevronLeft, ChevronRight, X } from 'lucide-react';
import { OptimizedImage } from '@/components/ui/optimized-image';

// Image data - images are in public/Product Pictures/
const productImages = [
  { id: 1, src: '/Product Pictures/1 (1).png', alt: 'Coffee packaging mockup by Econic Media – dramatic side lighting with shadow details' },
  { id: 2, src: '/Product Pictures/1 (2).png', alt: 'Cosmetic bottle product photography by Econic Media – clean white background with soft reflections' },
  { id: 3, src: '/Product Pictures/1 (3).png', alt: 'Supplement packaging visualization by Econic Media – studio lighting with product feature highlight' },
  { id: 4, src: '/Product Pictures/1 (4).png', alt: 'Skincare product packaging design by Econic Media – lifestyle arrangement with botanical elements' },
  { id: 5, src: '/Product Pictures/1 (5).png', alt: 'Premium perfume bottle photography by Econic Media – luxury product display with gradient background' },
  { id: 6, src: '/Product Pictures/1 (6).png', alt: 'Health supplement jar design by Econic Media – professional product visualization on dark background' },
  { id: 7, src: '/Product Pictures/1 (7).png', alt: 'Organic tea packaging mockup by Econic Media – natural styling with ingredient accent' },
  { id: 8, src: '/Product Pictures/1 (8).png', alt: 'Wellness product photography by Econic Media – minimalist composition with brand message focus' },
  { id: 9, src: '/Product Pictures/1 (9).png', alt: 'Vitamin bottle packaging design by Econic Media – e-commerce ready product visual with detail shot' },
  { id: 10, src: '/Product Pictures/1 (10).png', alt: 'CBD oil product visualization by Econic Media – transparent bottle display showing liquid quality' },
  { id: 11, src: '/Product Pictures/1 (11).png', alt: 'Protein powder packaging photography by Econic Media – fitness product with dynamic angle shot' },
  { id: 12, src: '/Product Pictures/1 (12).png', alt: 'Beauty serum bottle design by Econic Media – premium product visualization with droplet accent' },
  { id: 13, src: '/Product Pictures/1 (13).png', alt: 'Herbal supplement packaging mockup by Econic Media – natural health product visual with ingredients' },
  { id: 14, src: '/Product Pictures/1 (14).png', alt: 'Facial cream jar photography by Econic Media – beauty product with texture highlight' },
  { id: 15, src: '/Product Pictures/1 (15).png', alt: 'Nutrition bar packaging design by Econic Media – snack product with flavor visual emphasis' },
  { id: 16, src: '/Product Pictures/1 (16).png', alt: 'Essential oil bottle visualization by Econic Media – aromatherapy product with natural backdrop' },
  { id: 17, src: '/Product Pictures/1 (17).png', alt: 'Collagen supplement packaging photography by Econic Media – beauty nutrition product with elegant styling' },
  { id: 18, src: '/Product Pictures/1 (18).png', alt: 'Plant-based powder container design by Econic Media – vegan product visual with ingredient scattering' },
  { id: 19, src: '/Product Pictures/1 (19).png', alt: 'Hair care bottle mockup by Econic Media – salon quality product visualization with gradient background' },
  { id: 20, src: '/Product Pictures/1 (20).png', alt: 'Vitamin gummy package photography by Econic Media – supplement product with playful arrangement' },
  { id: 21, src: '/Product Pictures/1 (21).png', alt: 'Sports nutrition container design by Econic Media – fitness product visual with dynamic lighting' },
  { id: 22, src: '/Product Pictures/1 (22).png', alt: 'Organic skincare set visualization by Econic Media – beauty product collection with botanical styling' },
  { id: 23, src: '/Product Pictures/1 (23).png', alt: 'Health drink bottle mockup by Econic Media – beverage product with condensation detail' },
  { id: 24, src: '/Product Pictures/1 (24).png', alt: 'Natural supplement packaging photography by Econic Media – wellness product with ingredient feature' },
];

// Define how many images to show in each row based on screen size
const imagesPerRow = {
  sm: 1,
  md: 2,
  lg: 3,
  xl: 4
};

// Calculate how many images to show initially (2 rows)
const initialImagesToShow = imagesPerRow.xl * 2; // 8 images (2 rows of 4 on desktop)

const ImageViewer = ({
  image,
  onClose,
  onNext,
  onPrev,
  currentIndex,
  totalImages
}: {
  image: { id: number; src: string; alt: string };
  onClose: () => void;
  onNext: () => void;
  onPrev: () => void;
  currentIndex: number;
  totalImages: number;
}) => {
  // Add keyboard navigation for the viewer
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowRight') {
        onNext();
      } else if (e.key === 'ArrowLeft') {
        onPrev();
      } else if (e.key === 'Escape') {
        onClose();
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [onNext, onPrev, onClose]);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-2 sm:p-4 md:p-6 bg-black/90 backdrop-blur-sm" onClick={onClose}>
      <div
        className="gallery-modal-container luxury-card w-full relative overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Enhanced premium glow effects for modal */}
        <div className="absolute -inset-2 rounded-3xl bg-gradient-aurora opacity-20 blur-xl z-0"></div>
        <div className="absolute -inset-1 rounded-2xl bg-gradient-luxury opacity-10 blur-lg z-0"></div>

        <div className="flex justify-between items-center mb-2 sm:mb-4 relative z-10">
          <button
            type="button"
            onClick={onClose}
            className="text-gray-400 hover:text-premium-gold transition-colors duration-300 p-2 min-w-[48px] min-h-[48px] rounded-full hover:bg-white/10 touch-manipulation"
            aria-label="Close image preview"
          >
            <X size={24} />
          </button>
        </div>

        <div className="gallery-modal-image-container relative flex justify-center items-center p-2 sm:p-3 md:p-6">
          <motion.div
            key={image.id}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="gallery-modal-image-wrapper"
          >
            <OptimizedImage
              src={image.src}
              alt={image.alt}
              className="gallery-modal-image object-contain rounded-md"
              objectFit="contain"
              priority={true}
            />
          </motion.div>

          <button
            type="button"
            className="absolute left-1 sm:left-2 md:left-4 top-1/2 -translate-y-1/2 premium-glass hover:border-premium-gold/30 hover:shadow-[0_4px_20px_rgba(255,215,0,0.3)] p-2 sm:p-3 min-w-[48px] min-h-[48px] rounded-full text-white transition-all duration-300 hover:scale-110 touch-manipulation"
            onClick={(e) => { e.stopPropagation(); onPrev(); }}
            aria-label="Previous image"
          >
            <ChevronLeft size={20} className="text-premium-gold hidden sm:block" />
            <ChevronLeft size={24} className="text-premium-gold sm:hidden" />
          </button>

          <button
            type="button"
            className="absolute right-1 sm:right-2 md:right-4 top-1/2 -translate-y-1/2 premium-glass hover:border-premium-gold/30 hover:shadow-[0_4px_20px_rgba(255,215,0,0.3)] p-2 sm:p-3 min-w-[48px] min-h-[48px] rounded-full text-white transition-all duration-300 hover:scale-110 touch-manipulation"
            onClick={(e) => { e.stopPropagation(); onNext(); }}
            aria-label="Next image"
          >
            <ChevronRight size={20} className="text-premium-gold hidden sm:block" />
            <ChevronRight size={24} className="text-premium-gold sm:hidden" />
          </button>
        </div>

        <div className="flex justify-center mt-4 gap-1 relative z-10">
          <p className="text-sm text-gradient-aurora font-medium">
            {currentIndex + 1} of {totalImages}
          </p>
        </div>
      </div>
    </div>
  );
};

const GallerySection: React.FC = () => {
  const [selectedImageId, setSelectedImageId] = useState<number | null>(null);
  const [showAll, setShowAll] = useState(false);

  // Determine which images to display
  const visibleImages = showAll ? productImages : productImages.slice(0, initialImagesToShow);

  // Get the selected image object and index
  const selectedImageIndex = selectedImageId !== null
    ? productImages.findIndex(img => img.id === selectedImageId)
    : -1;

  const selectedImage = selectedImageIndex !== -1
    ? productImages[selectedImageIndex]
    : null;

  // Navigate to next or previous image
  const navigateImage = (direction: 'next' | 'prev') => {
    if (selectedImageIndex === -1) return;

    let newIndex: number;

    if (direction === 'next') {
      newIndex = (selectedImageIndex + 1) % productImages.length;
    } else {
      newIndex = (selectedImageIndex - 1 + productImages.length) % productImages.length;
    }

    setSelectedImageId(productImages[newIndex].id);
  };

  return (
    <section id="gallery" className="section-padding relative overflow-hidden">
      {/* Unified background layers for optimal readability and visual harmony */}
      <div className="absolute inset-0 bg-gradient-cosmic-dark"></div>
      <div className="absolute inset-0 bg-gradient-cosmic opacity-50"></div>
      <div className="absolute inset-0 bg-gradient-radial from-neon-purple/4 via-transparent to-neon-cyan/2"></div>
      <div className="absolute inset-0 bg-gradient-aurora opacity-1"></div>
      <div className="absolute inset-0 bg-black/40"></div>

      {/* Enhanced gradient orb decorations - Static - Optimized for mobile */}
      <div className="absolute top-20 right-1/4 w-40 h-40 sm:w-48 sm:h-48 md:w-64 md:h-64 bg-gradient-ocean opacity-20 sm:opacity-30 rounded-full blur-2xl sm:blur-3xl"></div>
      <div className="absolute bottom-20 left-1/4 w-48 h-48 sm:w-64 sm:h-64 md:w-80 md:h-80 bg-gradient-sunset opacity-20 sm:opacity-25 rounded-full blur-2xl sm:blur-3xl"></div>
      <div className="absolute top-1/2 left-1/2 w-60 h-60 sm:w-80 sm:h-80 md:w-96 md:h-96 bg-gradient-aurora opacity-5 sm:opacity-10 rounded-full blur-2xl sm:blur-3xl"></div>

      {/* Premium floating particles - Static */}
      <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-premium-gold rounded-full opacity-60"></div>
      <div className="absolute top-3/4 right-1/3 w-1 h-1 bg-premium-silver rounded-full opacity-40"></div>
      <div className="absolute top-1/3 right-1/4 w-1.5 h-1.5 bg-premium-platinum rounded-full opacity-50"></div>

      {/* Decorative Camera Icon (Top Right) - Gallery Context - Static - Responsive for mobile */}
      <div className="absolute top-0 right-0 w-16 h-16 sm:w-24 sm:h-24 md:w-32 md:h-32 lg:w-48 lg:h-48 text-neon-cyan/20 transform translate-x-1/4 -translate-y-1/4 sm:translate-x-1/3 sm:-translate-y-1/3 rotate-12 opacity-40 sm:opacity-50">
        <Camera className="w-full h-full" />
      </div>

      {/* Decorative Aperture Icon (Bottom Left) - Gallery Context - Static - Responsive for mobile */}
      <div className="absolute bottom-0 left-0 w-16 h-16 sm:w-24 sm:h-24 md:w-32 md:h-32 lg:w-48 lg:h-48 text-neon-purple/20 transform -translate-x-1/4 translate-y-1/4 sm:-translate-x-1/3 sm:translate-y-1/3 -rotate-12 opacity-40 sm:opacity-50">
        <Aperture className="w-full h-full" />
      </div>

      <div className="container max-w-7xl mx-auto relative z-10 px-3 sm:px-4 md:px-6">
        <div className="text-center">
          <ScrollReveal>
            <div className="inline-block mb-3 sm:mb-4 px-4 sm:px-6 py-1.5 sm:py-2 rounded-full premium-glass text-xs sm:text-sm font-medium">
              <span className="text-gradient-aurora">✨ Visual Portfolio</span>
            </div>
          </ScrollReveal>

          <ScrollReveal delay={200}>
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-gradient-aurora drop-shadow-glow mb-6 sm:mb-8 md:mb-12">
              Our Visual Craftsmanship
            </h2>
          </ScrollReveal>

          <ScrollReveal delay={400}>
            <p className="text-base sm:text-lg text-foreground/80 mb-6 sm:mb-8 max-w-2xl mx-auto">
              Explore our product photography and website designs created to elevate brand identity and boost conversions.
            </p>
          </ScrollReveal>
        </div>

        <motion.div
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6"
          layout
          transition={{
            duration: 0.6,
            type: "spring",
            stiffness: 100,
            damping: 15
          }}
          id="gallery-grid"
        >
          <AnimatePresence>
            {visibleImages.map((item, index) => (
              <motion.div
                key={item.id}
                layout
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{
                  duration: 0.4,
                  delay: Math.min(index * 0.05, 0.5),
                  ease: "easeOut"
                }}
              >
                <div className="group relative overflow-hidden rounded-xl sm:rounded-2xl luxury-card hover:shadow-neon-glow transition-all duration-500 h-full flex flex-col hover:translate-y-[-4px] hover:border-neon-cyan touch-manipulation">
                  {/* Enhanced premium glow effects - Optimized for mobile */}
                  <div className="absolute -inset-1 sm:-inset-2 rounded-2xl sm:rounded-3xl bg-gradient-aurora opacity-0 group-hover:opacity-30 sm:group-hover:opacity-40 blur-lg sm:blur-xl z-0 transition-opacity duration-500"></div>
                  <div className="absolute -inset-0.5 sm:-inset-1 rounded-xl sm:rounded-2xl bg-gradient-luxury opacity-0 group-hover:opacity-20 blur-md sm:blur-lg z-0 transition-opacity duration-500"></div>

                  {/* Premium border frame */}
                  <div className="absolute -inset-0.5 rounded-2xl bg-gradient-premium opacity-0 group-hover:opacity-60 z-0 transition-opacity duration-500"></div>

                  <div
                    className="relative w-full aspect-[4/3] overflow-hidden cursor-pointer z-10"
                    onClick={() => setSelectedImageId(item.id)}
                  >
                    <OptimizedImage
                      src={item.src}
                      alt={item.alt}
                      className="w-full h-full transition-transform duration-700 group-hover:scale-105"
                      objectFit="cover"
                      loading="lazy"
                      priority={index < 4} // Prioritize first 4 visible images (above the fold)
                    />
                    {/* Enhanced gradient overlay for image */}
                    <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-black/10 group-hover:from-black/20 transition-all duration-500"></div>
                  </div>

                  {/* Premium accent elements - Static */}
                  <div className="absolute -top-2 -right-2 w-4 h-4 bg-premium-gold rounded-full opacity-0 group-hover:opacity-80 z-20 transition-opacity duration-500"></div>
                  <div className="absolute -bottom-2 -left-2 w-3 h-3 bg-premium-silver rounded-full opacity-0 group-hover:opacity-60 z-20 transition-opacity duration-500"></div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>

        {productImages.length > initialImagesToShow && (
          <motion.div
            className="flex justify-center mt-8 sm:mt-10 md:mt-16"
            layout
            transition={{ duration: 0.4 }}
          >
            <button
              type="button"
              onClick={() => setShowAll(!showAll)}
              className="flex items-center justify-center gap-2 sm:gap-3 px-5 sm:px-6 md:px-8 py-2.5 sm:py-3 md:py-4 min-h-[48px] min-w-[120px] premium-glass hover:border-premium-gold/30 hover:shadow-[0_8px_30px_rgba(255,215,0,0.3)] text-white rounded-xl font-semibold transition-all duration-500 group hover:scale-105 active:scale-95 touch-manipulation"
            >
              <span className="text-gradient-aurora">{showAll ? 'Show Less' : 'Show More'}</span>
              <motion.div
                animate={{ rotate: showAll ? 180 : 0 }}
                transition={{ duration: 0.5, ease: "easeInOut" }}
              >
                <ChevronDown className="w-5 h-5 text-premium-gold group-hover:text-neon-cyan transition-colors duration-300" />
              </motion.div>
            </button>
          </motion.div>
        )}
      </div>

      {/* Image Viewer Modal */}
      <AnimatePresence>
        {selectedImage && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            <ImageViewer
              image={selectedImage}
              onClose={() => setSelectedImageId(null)}
              onNext={() => navigateImage('next')}
              onPrev={() => navigateImage('prev')}
              currentIndex={selectedImageIndex}
              totalImages={productImages.length}
            />
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  );
};

export default GallerySection;
